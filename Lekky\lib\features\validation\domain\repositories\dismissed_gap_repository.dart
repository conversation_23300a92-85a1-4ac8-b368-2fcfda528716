// File: lib/features/validation/domain/repositories/dismissed_gap_repository.dart

import '../models/dismissed_gap.dart';

/// Repository interface for dismissed gaps
abstract class DismissedGapRepository {
  /// Add a new dismissed gap
  Future<int> addDismissedGap(DismissedGap gap);

  /// Get all dismissed gaps
  Future<List<DismissedGap>> getAllDismissedGaps();

  /// Get dismissed gaps within a date range
  Future<List<DismissedGap>> getDismissedGapsInRange(
    DateTime startDate,
    DateTime endDate,
  );

  /// Delete a dismissed gap by ID
  Future<void> deleteDismissedGap(int id);

  /// Delete all dismissed gaps
  Future<void> deleteAllDismissedGaps();

  /// Check if a gap period is dismissed
  Future<bool> isGapDismissed(DateTime gapStart, DateTime gapEnd);
}
