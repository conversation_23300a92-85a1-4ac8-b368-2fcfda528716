// File: lib/features/validation/domain/services/data_integrity_service.dart
import 'dart:async';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/entry_filter_utils.dart';
import '../../../../core/shared/enums/entry_enums.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/services/status_field_migration_service.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../../../top_ups/domain/repositories/top_up_repository.dart';
import 'meter_reading_validator.dart';
import '../models/integrity_report.dart';
import '../models/validation_issue.dart';
import '../utils/validation_message_builder.dart';
import 'simple_gap_detection_service.dart';
import 'dismissed_entry_service.dart';
import 'validation_status_synchronizer.dart';

/// Service for checking data integrity and validating entries
class DataIntegrityService {
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;
  final MeterReadingValidator _validator;
  final logger = Logger('DataIntegrityService');

  /// Constructor
  DataIntegrityService({
    required MeterReadingRepository meterReadingRepository,
    required TopUpRepository topUpRepository,
    MeterReadingValidator? validator,
  })  : _meterReadingRepository = meterReadingRepository,
        _topUpRepository = topUpRepository,
        _validator = validator ??
            MeterReadingValidator(
              meterReadingRepository: meterReadingRepository,
              topUpRepository: topUpRepository,
            );

  /// Check the integrity of all data
  ///
  /// Returns an IntegrityReport with the results
  ///
  /// Note: This method still checks for negative values and future dates in existing data
  /// to handle data that might have been imported or created before validation was added
  /// at the point of entry.
  Future<IntegrityReport> checkIntegrity() async {
    final stopwatch = Stopwatch()..start();
    final issues = <ValidationIssue>[];

    try {
      // Implement pagination to retrieve data in chunks
      final int pageSize = 100; // Define page size
      int page = 0;

      List<MeterReading> allMeterReadings = [];
      List<TopUp> allTopUps = [];

      // Paginate meter readings
      while (true) {
        final meterReadings = await _meterReadingRepository.getMeterReadings(
            page: page, pageSize: pageSize);
        if (meterReadings.isEmpty) break;
        allMeterReadings.addAll(meterReadings);
        page++;
      }

      // Paginate top-ups
      page = 0;
      while (true) {
        final topUps =
            await _topUpRepository.getTopUps(page: page, pageSize: pageSize);
        if (topUps.isEmpty) break;
        allTopUps.addAll(topUps);
        page++;
      }

      final meterReadings = allMeterReadings;
      final topUps = allTopUps;

      final totalEntries = meterReadings.length + topUps.length;

      // Check for chronological order in meter readings
      issues.addAll(await _checkChronologicalOrder(meterReadings));

      // Check for negative values
      issues.addAll(await _checkNegativeValues(meterReadings, topUps));

      // Check for future dates
      issues.addAll(await _checkFutureDates(meterReadings, topUps));

      // Check for balance inconsistencies
      issues.addAll(await _checkBalanceConsistency(meterReadings, topUps));

      // Check for duplicate entries
      issues.addAll(await _checkDuplicateEntries(meterReadings, topUps));

      // Check for missing entries (large gaps)
      issues.addAll(await _checkMissingEntries(meterReadings, topUps));

      // Count valid and invalid entries (excluding dismissed entries from invalid count)
      final totalInvalidMeterReadings = meterReadings
          .where((r) => !r.isValid && !EntryFilterUtils.isDismissedEntry(r))
          .length;
      final totalInvalidTopUps = topUps.where((t) => !t.isValid).length;
      final totalInvalidEntries =
          totalInvalidMeterReadings + totalInvalidTopUps;
      final validEntriesCount = totalEntries - totalInvalidEntries;

      // Synchronize entry status with validation issues
      await ValidationStatusSynchronizer.syncEntryStatus(
        meterReadings,
        topUps,
        issues,
      );

      stopwatch.stop();

      return IntegrityReport(
        issues: issues,
        totalEntriesChecked: totalEntries,
        validEntriesCount: validEntriesCount,
        invalidEntriesCount: totalInvalidEntries,
        checkDuration: stopwatch.elapsed,
      );
    } catch (e) {
      logger.e('Error checking data integrity', details: e.toString());

      stopwatch.stop();

      // Return a report with the error
      return IntegrityReport(
        issues: [
          ValidationIssue(
            type: ValidationIssueType.other,
            severity: ValidationIssueSeverity.high,
            message: 'Error checking data integrity: $e',
          ),
        ],
        totalEntriesChecked: 0,
        validEntriesCount: 0,
        invalidEntriesCount: 0,
        checkDuration: stopwatch.elapsed,
      );
    }
  }

  /// Validate all entries and return a list of issues
  Future<List<ValidationIssue>> validateAllEntries() async {
    try {
      final report = await checkIntegrity();
      return report.issues;
    } catch (e) {
      logger.e('Error validating all entries', details: e.toString());
      return [
        ValidationIssue(
          type: ValidationIssueType.other,
          severity: ValidationIssueSeverity.high,
          message: 'Error validating entries: $e',
        ),
      ];
    }
  }

  /// Check for chronological order issues in meter readings
  Future<List<ValidationIssue>> _checkChronologicalOrder(
      List<MeterReading> readings) async {
    final issues = <ValidationIssue>[];

    // Filter out dismissed entries before validation
    final validReadings = EntryFilterUtils.filterValidMeterReadings(readings);

    // Sort readings by date
    final sortedReadings = List<MeterReading>.from(validReadings)
      ..sort((a, b) => a.date.compareTo(b.date));

    // Check if any reading has a date before the previous one
    for (int i = 1; i < sortedReadings.length; i++) {
      final current = sortedReadings[i];
      final previous = sortedReadings[i - 1];

      if (current.date.isBefore(previous.date)) {
        // Generate detailed message using centralized builder
        final message =
            await ValidationMessageBuilder.buildChronologicalOrderMessage(
          current: current,
          previousDate: previous.date,
        );

        issues.add(ValidationIssue(
          entryId: current.id,
          type: ValidationIssueType.chronologicalOrder,
          severity: ValidationIssueSeverity.high,
          message: message,
          metadata: {
            'current_id': current.id,
            'current_date': current.date.toIso8601String(),
            'previous_id': previous.id,
            'previous_date': previous.date.toIso8601String(),
          },
        ));
      }
    }

    return issues;
  }

  /// Check for negative values in meter readings and top-ups
  Future<List<ValidationIssue>> _checkNegativeValues(
      List<MeterReading> readings, List<TopUp> topUps) async {
    final issues = <ValidationIssue>[];

    // Filter out dismissed entries before validation
    final validReadings = EntryFilterUtils.filterValidMeterReadings(readings);

    // Check meter readings
    for (final reading in validReadings) {
      if (reading.value < 0) {
        // Generate detailed message using centralized builder
        final message =
            await ValidationMessageBuilder.buildNegativeMeterReadingMessage(
          reading: reading,
        );

        issues.add(ValidationIssue(
          entryId: reading.id,
          type: ValidationIssueType.negativeValue,
          severity: ValidationIssueSeverity.high,
          message: message,
          metadata: {
            'entry_id': reading.id,
            'value': reading.value,
            'date': reading.date.toIso8601String(),
          },
        ));
      }
    }

    // Check top-ups
    for (final topUp in topUps) {
      if (topUp.amount < 0) {
        // Generate detailed message using centralized builder
        final message =
            await ValidationMessageBuilder.buildNegativeTopUpMessage(
          topUp: topUp,
        );

        issues.add(ValidationIssue(
          entryId: topUp.id,
          type: ValidationIssueType.negativeValue,
          severity: ValidationIssueSeverity.high,
          message: message,
          metadata: {
            'entry_id': topUp.id,
            'amount': topUp.amount,
            'date': topUp.date.toIso8601String(),
          },
        ));
      }
    }

    return issues;
  }

  /// Check for future dates in meter readings and top-ups
  Future<List<ValidationIssue>> _checkFutureDates(
      List<MeterReading> readings, List<TopUp> topUps) async {
    final issues = <ValidationIssue>[];
    final now = DateTime.now();

    // Filter out dismissed entries before validation
    final validReadings = EntryFilterUtils.filterValidMeterReadings(readings);

    // Check meter readings
    for (final reading in validReadings) {
      if (reading.date.isAfter(now)) {
        // Generate detailed message using centralized builder
        final message =
            await ValidationMessageBuilder.buildFutureDateMeterReadingMessage(
          reading: reading,
        );

        issues.add(ValidationIssue(
          entryId: reading.id,
          type: ValidationIssueType.futureDate,
          severity: ValidationIssueSeverity.medium,
          message: message,
          metadata: {
            'entry_id': reading.id,
            'date': reading.date.toIso8601String(),
            'current_date': now.toIso8601String(),
          },
        ));
      }
    }

    // Check top-ups
    for (final topUp in topUps) {
      if (topUp.date.isAfter(now)) {
        // Generate detailed message using centralized builder
        final message =
            await ValidationMessageBuilder.buildFutureDateTopUpMessage(
          topUp: topUp,
        );

        issues.add(ValidationIssue(
          entryId: topUp.id,
          type: ValidationIssueType.futureDate,
          severity: ValidationIssueSeverity.medium,
          message: message,
          metadata: {
            'entry_id': topUp.id,
            'date': topUp.date.toIso8601String(),
            'current_date': now.toIso8601String(),
          },
        ));
      }
    }

    return issues;
  }

  /// Trigger validation recalculation to clear false positives on dismissed entries
  Future<void> recalculateValidationAfterDismissal() async {
    try {
      Logger.info(
          'DataIntegrityService: Recalculating validation after dismissal');

      // Clean up any orphaned dismissed entries first
      final dismissedEntryService = serviceLocator<DismissedEntryService>();
      await dismissedEntryService.cleanupInvalidDismissalEntries();

      // Run status field synchronization to ensure consistency
      await StatusFieldMigrationService.runStatusFieldSynchronization();

      // Re-validate all entries to clear any false positives
      await validateAndUpdateAllMeterReadings();

      Logger.info('DataIntegrityService: Validation recalculation completed');
    } catch (e) {
      Logger.error(
          'DataIntegrityService: Error during validation recalculation: $e');
    }
  }

  /// Force complete cleanup and re-validation (for fixing gap detection issues)
  Future<void> forceCompleteValidationRefresh() async {
    try {
      Logger.info('DataIntegrityService: Starting complete validation refresh');

      // 1. Clean up all orphaned dismissed entries
      final dismissedEntryService = serviceLocator<DismissedEntryService>();
      await dismissedEntryService.cleanupInvalidDismissalEntries();

      // 2. Run status field synchronization
      await StatusFieldMigrationService.runStatusFieldSynchronization();

      // 3. Re-validate all entries with new logic
      await validateAndUpdateAllMeterReadings();
      await validateAndUpdateAllTopUps();

      Logger.info(
          'DataIntegrityService: Complete validation refresh completed');
    } catch (e) {
      Logger.error(
          'DataIntegrityService: Error during complete validation refresh: $e');
      rethrow;
    }
  }

  /// Check for balance inconsistencies between meter readings and top-ups
  Future<List<ValidationIssue>> _checkBalanceConsistency(
      List<MeterReading> readings, List<TopUp> topUps) async {
    final issues = <ValidationIssue>[];

    // Filter out dismissed entries before validation
    final validReadings = EntryFilterUtils.filterValidMeterReadings(readings);

    // Use the centralized validator to check each reading
    final validationResults =
        await _validator.validateMeterReadings(validReadings);

    // Sort readings by date for proper expected value calculation
    final sortedReadings = List<MeterReading>.from(validReadings)
      ..sort((a, b) => a.date.compareTo(b.date));

    for (final reading in sortedReadings) {
      final isValid = validationResults[reading.id!] ?? true;

      if (!isValid) {
        // Calculate expected value for the error message
        final expectedValue =
            await _validator.calculateExpectedReading(reading);

        logger.i(
            'Creating balance inconsistency issue for reading ID: ${reading.id}, entryId will be: ${reading.id}');

        // Generate detailed message using centralized builder
        final message =
            await ValidationMessageBuilder.buildBalanceInconsistencyMessage(
          reading: reading,
          expectedValue: expectedValue,
        );

        issues.add(ValidationIssue(
          entryId: reading.id,
          type: ValidationIssueType.balanceInconsistency,
          severity: ValidationIssueSeverity.high,
          message: message,
          metadata: {
            'entry_id': reading.id,
            'current_value': reading.value,
            'current_date': reading.date.toIso8601String(),
            if (expectedValue != null) 'expected_value': expectedValue,
            if (expectedValue != null)
              'difference': (reading.value - expectedValue).abs(),
          },
        ));
        logger.i(
            'Balance inconsistency issue created with entryId: ${reading.id}');
      }
    }

    return issues;
  }

  /// Check for duplicate entries
  Future<List<ValidationIssue>> _checkDuplicateEntries(
      List<MeterReading> readings, List<TopUp> topUps) async {
    final issues = <ValidationIssue>[];

    // Check for duplicate meter readings
    final readingDates = <String, List<MeterReading>>{};
    for (final reading in readings) {
      final dateStr = _formatDateForComparison(reading.date);
      readingDates[dateStr] = readingDates[dateStr] ?? [];
      readingDates[dateStr]!.add(reading);
    }

    // Find dates with multiple readings
    for (final entry in readingDates.entries) {
      if (entry.value.length > 1) {
        // Create an issue for each duplicate (except the first one)
        for (int i = 1; i < entry.value.length; i++) {
          // Generate detailed message using centralized builder
          final message =
              await ValidationMessageBuilder.buildDuplicateMeterReadingMessage(
            reading: entry.value[i],
            duplicateCount: entry.value.length,
          );

          issues.add(ValidationIssue(
            entryId: entry.value[i].id,
            type: ValidationIssueType.duplicateEntry,
            severity: ValidationIssueSeverity.medium,
            message: message,
            metadata: {
              'entry_id': entry.value[i].id,
              'date': entry.value[i].date.toIso8601String(),
              'duplicate_count': entry.value.length,
              'original_id': entry.value[0].id,
            },
          ));
        }
      }
    }

    // Check for duplicate top-ups
    final topUpDates = <String, List<TopUp>>{};
    for (final topUp in topUps) {
      final dateStr = _formatDateForComparison(topUp.date);
      topUpDates[dateStr] = topUpDates[dateStr] ?? [];
      topUpDates[dateStr]!.add(topUp);
    }

    // Find dates with multiple top-ups
    for (final entry in topUpDates.entries) {
      if (entry.value.length > 1) {
        // Create an issue for each duplicate (except the first one)
        for (int i = 1; i < entry.value.length; i++) {
          // Generate detailed message using centralized builder
          final message =
              await ValidationMessageBuilder.buildDuplicateTopUpMessage(
            topUp: entry.value[i],
            duplicateCount: entry.value.length,
          );

          issues.add(ValidationIssue(
            entryId: entry.value[i].id,
            type: ValidationIssueType.duplicateEntry,
            severity: ValidationIssueSeverity.low, // Lower severity for top-ups
            message: message,
            metadata: {
              'entry_id': entry.value[i].id,
              'date': entry.value[i].date.toIso8601String(),
              'duplicate_count': entry.value.length,
              'original_id': entry.value[0].id,
            },
          ));
        }
      }
    }

    return issues;
  }

  /// Check for missing entries (large gaps) using new simple gap detection
  Future<List<ValidationIssue>> _checkMissingEntries(
      List<MeterReading> readings, List<TopUp> topUps) async {
    try {
      // Use the new simple gap detection service
      final gapDetectionService = serviceLocator<SimpleGapDetectionService>();
      return await gapDetectionService.findMissingEntryGaps(readings, topUps);
    } catch (e) {
      Logger.error('DataIntegrityService: Error during gap detection: $e');
      return [];
    }
  }

  /// Format a date for comparison (strip time component)
  String _formatDateForComparison(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// Validate a single entry and return a list of validation issues
  ///
  /// Note: Negative Value Check and Future Date Check are performed at the point of entry
  /// (when adding or editing entries) in the EntryController, not here.
  Future<List<ValidationIssue>> validateSingleEntry(dynamic entry) async {
    try {
      final issues = <ValidationIssue>[];

      // Only meter readings can have validation issues
      if (entry is! MeterReading) {
        return [];
      }

      // Use the centralized validator for balance consistency checking
      final isValid = await _validator.validateMeterReading(entry);

      if (!isValid) {
        // Calculate expected value for the error message
        final expectedValue = await _validator.calculateExpectedReading(entry);

        // Generate detailed message using centralized builder
        final message =
            await ValidationMessageBuilder.buildBalanceInconsistencyMessage(
          reading: entry,
          expectedValue: expectedValue,
        );

        issues.add(ValidationIssue(
          entryId: entry.id,
          type: ValidationIssueType.balanceInconsistency,
          severity: ValidationIssueSeverity.high,
          message: message,
          metadata: {
            'entry_id': entry.id,
            'current_value': entry.value,
            'current_date': entry.date.toIso8601String(),
            if (expectedValue != null) 'expected_value': expectedValue,
            if (expectedValue != null)
              'difference': (entry.value - expectedValue).abs(),
          },
        ));
      }

      return issues;
    } catch (e) {
      logger.e('Error validating entry', details: e.toString());
      return [
        ValidationIssue(
          type: ValidationIssueType.other,
          severity: ValidationIssueSeverity.high,
          message: 'Error validating entry: $e',
        ),
      ];
    }
  }

  /// Validate all meter readings and update their isValid flags
  Future<void> validateAndUpdateAllMeterReadings() async {
    try {
      // Implement pagination to retrieve data in chunks
      final int pageSize = 100; // Define page size
      int page = 0;

      List<MeterReading> allMeterReadings = [];

      // Paginate meter readings
      while (true) {
        final meterReadings = await _meterReadingRepository.getMeterReadings(
            page: page, pageSize: pageSize);
        if (meterReadings.isEmpty) break;
        allMeterReadings.addAll(meterReadings);
        page++;
      }

      final meterReadings = allMeterReadings;
      
=======
  Future<void> validateAndUpdateAllMeterReadings() async {
    try {
      final meterReadings = await _meterReadingRepository.getAllMeterReadings();

      // Filter out dismissed entries before validation
      final validReadings =
          EntryFilterUtils.filterValidMeterReadings(meterReadings);

      // Use the centralized validator for efficient batch validation
      final validationResults =
          await _validator.validateMeterReadings(validReadings);

      for (final reading in validReadings) {
        final shouldBeValid = validationResults[reading.id!] ?? true;

        // Update status if it doesn't match the validation result
        final expectedStatus =
            shouldBeValid ? EntryStatus.valid : EntryStatus.invalid;
        if (reading.status != expectedStatus) {
          final updatedReading = reading.copyWith(status: expectedStatus);
          await _meterReadingRepository.updateMeterReading(updatedReading);
        }
      }
    } catch (e) {
      logger.e('Error validating and updating all meter readings',
          details: e.toString());
    }
  }

  /// Validate all top-ups and update their isValid flags
  Future<void> validateAndUpdateAllTopUps() async {
    try {
      final topUps = await _topUpRepository.getAllTopUps();

      // Use the centralized validator for efficient batch validation
      final validationResults = await _validator.validateTopUps(topUps);

      for (final topUp in topUps) {
        final shouldBeValid = validationResults[topUp.id!] ?? true;

        // Update status if it doesn't match the validation result
        final expectedStatus =
            shouldBeValid ? EntryStatus.valid : EntryStatus.invalid;
        if (topUp.status != expectedStatus) {
          final updatedTopUp = topUp.copyWith(status: expectedStatus);
          await _topUpRepository.updateTopUp(updatedTopUp);
        }
      }
    } catch (e) {
      logger.e('Error validating and updating all top-ups',
          details: e.toString());
    }
  }
}
