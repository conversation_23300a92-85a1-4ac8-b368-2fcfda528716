// File: lib/features/validation/domain/models/dismissed_gap.dart

/// Model for a dismissed gap period
class DismissedGap {
  /// Unique identifier
  final int? id;

  /// Start date of the dismissed gap
  final DateTime startDate;

  /// End date of the dismissed gap
  final DateTime endDate;

  /// When the gap was dismissed
  final DateTime dismissedAt;

  /// Constructor
  const DismissedGap({
    this.id,
    required this.startDate,
    required this.endDate,
    required this.dismissedAt,
  });

  /// Create a copy with optional new values
  DismissedGap copyWith({
    int? id,
    DateTime? startDate,
    DateTime? endDate,
    DateTime? dismissedAt,
  }) {
    return DismissedGap(
      id: id ?? this.id,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      dismissedAt: dismissedAt ?? this.dismissedAt,
    );
  }

  /// Convert to map for database storage
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'dismissed_at': dismissedAt.toIso8601String(),
    };
  }

  /// Create from database map
  factory DismissedGap.fromMap(Map<String, dynamic> map) {
    return DismissedGap(
      id: map['id'] as int?,
      startDate: DateTime.parse(map['start_date'] as String),
      endDate: DateTime.parse(map['end_date'] as String),
      dismissedAt: DateTime.parse(map['dismissed_at'] as String),
    );
  }

  /// Check if this dismissed gap overlaps with a given gap
  bool overlapsWithGap(DateTime gapStart, DateTime gapEnd) {
    // Check if the gaps overlap
    return startDate.isBefore(gapEnd) && endDate.isAfter(gapStart);
  }

  /// Get the number of days in this dismissed gap
  int get gapDays => endDate.difference(startDate).inDays;

  @override
  String toString() {
    return 'DismissedGap(id: $id, startDate: $startDate, endDate: $endDate, dismissedAt: $dismissedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DismissedGap &&
        other.id == id &&
        other.startDate.isAtSameMomentAs(startDate) &&
        other.endDate.isAtSameMomentAs(endDate) &&
        other.dismissedAt.isAtSameMomentAs(dismissedAt);
  }

  @override
  int get hashCode {
    return id.hashCode ^
        startDate.hashCode ^
        endDate.hashCode ^
        dismissedAt.hashCode;
  }
}
