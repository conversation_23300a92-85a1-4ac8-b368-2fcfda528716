// File: lib/features/validation/domain/services/simple_gap_detection_service.dart

import '../../../../core/utils/logger.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../models/dismissed_gap.dart';
import '../models/validation_issue.dart';
import '../repositories/dismissed_gap_repository.dart';
import '../utils/validation_message_builder.dart';

/// Simple, clean gap detection service
class SimpleGapDetectionService {
  final DismissedGapRepository _dismissedGapRepository;

  const SimpleGapDetectionService(this._dismissedGapRepository);

  /// Find all missing entry gaps (> 62 days) that haven't been dismissed
  Future<List<ValidationIssue>> findMissingEntryGaps(
    List<MeterReading> meterReadings,
    List<TopUp> topUps,
  ) async {
    try {
      Logger.info('SimpleGapDetectionService: Starting gap detection');

      // Get all real entries (no fake dismissed entries)
      final allEntries = <DateTime>[];
      
      // Add all meter readings (only real ones)
      for (final reading in meterReadings) {
        allEntries.add(reading.date);
      }
      
      // Add all top-ups
      for (final topUp in topUps) {
        allEntries.add(topUp.date);
      }

      // Sort all entries by date
      allEntries.sort();

      if (allEntries.length < 2) {
        Logger.info('SimpleGapDetectionService: Not enough entries for gap detection');
        return [];
      }

      final issues = <ValidationIssue>[];

      // Check each consecutive pair for gaps > 62 days
      for (int i = 1; i < allEntries.length; i++) {
        final previous = allEntries[i - 1];
        final current = allEntries[i];
        final gapDays = current.difference(previous).inDays;

        if (gapDays > 62) {
          // Check if this gap has been dismissed
          final isDismissed = await _dismissedGapRepository.isGapDismissed(previous, current);

          if (!isDismissed) {
            // Create validation issue for this gap
            final message = ValidationMessageBuilder.buildMissingEntryMessage(
              gapDays: gapDays,
              startDate: previous,
              endDate: current,
            );

            issues.add(ValidationIssue(
              type: ValidationIssueType.missingEntry,
              severity: ValidationIssueSeverity.low,
              message: message,
              metadata: {
                'start_date': previous.toIso8601String(),
                'end_date': current.toIso8601String(),
                'gap_days': gapDays,
              },
            ));

            Logger.info('SimpleGapDetectionService: Found gap of $gapDays days from $previous to $current');
          } else {
            Logger.info('SimpleGapDetectionService: Gap from $previous to $current is dismissed, skipping');
          }
        }
      }

      Logger.info('SimpleGapDetectionService: Found ${issues.length} missing entry gaps');
      return issues;
    } catch (e) {
      Logger.error('SimpleGapDetectionService: Error during gap detection: $e');
      return [];
    }
  }

  /// Dismiss a gap by creating a dismissed gap record
  Future<void> dismissGap(ValidationIssue issue) async {
    try {
      final startDate = DateTime.parse(issue.metadata!['start_date']);
      final endDate = DateTime.parse(issue.metadata!['end_date']);
      final gapDays = issue.metadata!['gap_days'] as int;

      Logger.info('SimpleGapDetectionService: Dismissing gap of $gapDays days from $startDate to $endDate');

      final dismissedGap = DismissedGap(
        startDate: startDate,
        endDate: endDate,
        dismissedAt: DateTime.now(),
      );

      await _dismissedGapRepository.addDismissedGap(dismissedGap);
      Logger.info('SimpleGapDetectionService: Successfully dismissed gap');
    } catch (e) {
      Logger.error('SimpleGapDetectionService: Error dismissing gap: $e');
      rethrow;
    }
  }

  /// Get all dismissed gaps
  Future<List<DismissedGap>> getAllDismissedGaps() async {
    return await _dismissedGapRepository.getAllDismissedGaps();
  }

  /// Remove a dismissed gap (un-dismiss)
  Future<void> removeDismissedGap(int id) async {
    try {
      await _dismissedGapRepository.deleteDismissedGap(id);
      Logger.info('SimpleGapDetectionService: Removed dismissed gap with ID $id');
    } catch (e) {
      Logger.error('SimpleGapDetectionService: Error removing dismissed gap: $e');
      rethrow;
    }
  }

  /// Clean up all dismissed gaps (for migration)
  Future<void> cleanupAllDismissedGaps() async {
    try {
      await _dismissedGapRepository.deleteAllDismissedGaps();
      Logger.info('SimpleGapDetectionService: Cleaned up all dismissed gaps');
    } catch (e) {
      Logger.error('SimpleGapDetectionService: Error cleaning up dismissed gaps: $e');
      rethrow;
    }
  }
}
